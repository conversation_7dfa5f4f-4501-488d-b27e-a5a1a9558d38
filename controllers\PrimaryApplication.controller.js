const Applicant = require("../schema/primaryApplication.model");
const testApplicant = require("../schema/primaryApplication.model");
const loadDetails = require("../schema/loanDetails.model");
const CollateralDetails = require("../schema/collateral.model");
const documentDetails = require("../schema/documentDetails.model");
const mongoose = require("mongoose");
const { mongooseValidateError, paginateModel } = require("../Utils/index");
const loanType = require("../schema/loanType");

const createPrimaryApplication = async (req, res) => {
  try {
    const applicantData = req.body;
    const { role, userId } = req.user;

    // Create new applicant document
    const newApplicant = new Applicant({
      ...applicantData,
      assignedTo: role === 'employee' ? userId : null
    });

    // Save to database
    const savedApplicant = await newApplicant.save();

    res.status(201).json({
      success: true,
      message: "Applicant created successfully",
      data: {
        _id: savedApplicant._id,
        fname: savedApplicant.fname,
        mname: savedApplicant.mname,
        lname: savedApplicant.lname,
        applicationType: savedApplicant.applicationType,
      },
    });
  } catch (error) {
    // Handle validation errors
    if (mongooseValidateError(res, error)) return;

    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

const transitionPrimaryApplicationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!id || typeof status !== "number") {
      return res.status(400).json({
        success: false,
        message: "Primary Application ID and numeric status are required",
      });
    }

    // Optional: Validate allowed numeric statuses
    const allowedStatuses = [1, 2, 3, 4]; // Add more statuses as needed
    if (!allowedStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid status value",
      });
    }

    const updatedApplication = await Applicant.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );

    if (!updatedApplication) {
      return res.status(404).json({
        success: false,
        message: "Primary Application not found",
      });
    }

    return res.status(200).json({
      success: true,
      message: `Status updated to ${status} successfully`,
      data: updatedApplication,
    });
  } catch (error) {
    console.error("Error updating status:", error);
    return res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

const saveLoanDetails = async (req, res) => {
  try {
    const { primaryApplicationId, ...loanData } = req.body;

    if (!primaryApplicationId) {
      return res.status(400).json({
        success: false,
        message: "ApplicationId is Required",
      });
    }

    const loanDetailsInstance = new loadDetails({
      primaryApplicationId,
      ...loanData,
    });

    const savedLoadDetails = await loanDetailsInstance.save();

    res.status(200).json({
      success: true,
      data: savedLoadDetails,
    });
  } catch (error) {
    // Handle validation errors
    if (mongooseValidateError(res, error)) return;

    console.log(error);

    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

const saveCollateralDetails = async (req, res) => {
  try {
    const {
      primaryApplicationId,
      propertyOwnerName,
      propertyAddress,
      propertyType,
      landArea,
      constructionArea,
      propertyValuation,
      valuationDate,
    } = req.body;

    // Optionally validate required fields here
    if (!primaryApplicationId || !propertyOwnerName) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Create and save the collateral details
    const collateral = await CollateralDetails.create({
      primaryApplicationId,
      propertyOwnerName,
      propertyAddress,
      propertyType,
      landArea,
      constructionArea,
      propertyValuation,
      valuationDate,
    });

    return res.status(200).json({
      message: "Collateral details saved successfully",
      data: collateral,
    });
  } catch (error) {
    console.error("Error saving collateral details:", error);

    // Ensure headers are not already sent
    if (!res.headersSent) {
      return res.status(500).json({ error: "Internal Server Error" });
    }
  }
};

const saveDocumentDetails = async (req, res) => {
  try {
    const { primaryApplicationId, ...documentData } = req.body;

    if (!primaryApplicationId) {
      return res.status(400).json({
        success: false,
        message: "ApplicationId is Required",
      });
    }

    const documentDetailsInstance = new documentDetails({
      primaryApplicationId,
      ...documentData,
    });

    const savedDocumentDetails = await documentDetailsInstance.save();

    res.status(200).json({
      success: true,
      data: savedDocumentDetails,
    });
  } catch (error) {
    // Handle validation errors
    if (mongooseValidateError(res, error)) return;

    console.log(error);

    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

const getPrimaryApplication = async (req, res) => {
  try {
    const { primaryApplicationId } = req.params;

    if (!primaryApplicationId) {
      return res.status(400).json({
        success: false,
        message: "Primary Application ID is required",
      });
    }

    // Fetch the primary application
    const primaryApplication = await Applicant.findById(primaryApplicationId);

    if (!primaryApplication) {
      return res
        .status(404)
        .json({ success: false, message: "Primary Application not found" });
    }

    // Fetch related data using the same primaryApplicationId
    const [loanData, collateralData, documentData] = await Promise.all([
      loadDetails.find({ primaryApplicationId }),
      CollateralDetails.find({ primaryApplicationId }),
      documentDetails.find({ primaryApplicationId }),
    ]);

    return res.status(200).json({
      success: true,
      data: {
        primaryApplication,
        loanData,
        collateralData,
        documentData,
      },
    });
  } catch (error) {
    console.error("Error fetching primary application details:", error);
    return res
      .status(500)
      .json({ success: false, message: "Server Error", error: error.message });
  }
};

const getLoanType = async (req, res) => {
  try {
    const loanData = await loanType.find();
    console.log("loanData -->", loanData);
    res.status(200).json({ success: true, data: loanData });
  } catch (error) {
    console.error("Error fetching loanData:", err);
    res.status(500).json({ message: "Server Error" });
  }
};

const getPrimaryApplicationList = async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    const filter = {};
    if (status !== undefined) {
      filter.status = parseInt(status); // Ensuring number type
    }

    const result = await paginateModel(Applicant, {
      page: parseInt(page),
      limit: parseInt(limit),
      filter,
      sort: { createdAt: -1 },
      populate: {
        path: "loanInfo.loanProductId",
        populate: {
          path: "loanTypeId", // Adjust if field name differs
        },
      },
    });

    res.status(200).json({
      success: true,
      data: result.data,
      pagination: {
        currentPage: result.currentPage,
        totalPages: result.totalPages,
        totalItems: result.totalItems,
      },
    });
  } catch (error) {
    console.error("Error in getPrimaryApplicationList:", error);
    res.status(500).json({ success: false, message: "Server Error" });
  }
};

const testPrimaryApp = async (req, res) => {
  try {
    const payload = req.body;

    const testApplicantData = new testApplicant(payload);

    const savedtestApplicant = await testApplicantData.save();

    console.log("payload -->", payload);

    res.status(500).json({
      success: true,
      data: savedtestApplicant,
    });
  } catch (error) {
    // Handle validation errors
    if (mongooseValidateError(res, error)) return;

    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

module.exports = {
  createPrimaryApplication,
  saveLoanDetails,
  saveCollateralDetails,
  saveDocumentDetails,
  getPrimaryApplication,
  testPrimaryApp,
  getLoanType,
  transitionPrimaryApplicationStatus,
  getPrimaryApplicationList,
};
