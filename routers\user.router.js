const express = require('express');
const router = express.Router();
const { authMiddleware, permissionMiddleware } = require('../middleware/auth.middleware');
const {
    getMyProfile,
    updateMyProfile,
    changePassword,
    uploadProfilePicture,
    getUserById,
    updateUser,
    registerUser
} = require('../controllers/userProfile.controller');
const upload = require('../middleware/upload.middleware');
const {
    validateRegister,
    validateUpdateProfile,
    validateChangePassword,
    validateAdminUpdateUser,
    validateObjectId
} = require('../validators');

// Apply auth middleware to all routes except registration
router.use((req, res, next) => {
    if (req.path === '/' && req.method === 'POST') {
        return next();
    }
    return authMiddleware(req, res, next);
});

// @desc    Register a new user (admin only)
// @route   POST /api/users
// @access  Private/Admin
router.post(
    '/',
    authMiddleware,
    permissionMiddleware('users:create'),
    validateRegister,
    registerUser
);

// @desc    Get current user's profile
// @route   GET /api/users/profile
// @access  Private
router.get('/profile', getMyProfile);

// @desc    Update current user's profile
// @route   PUT /api/users/profile
// @access  Private
router.put(
    '/profile',
    validateUpdateProfile,
    updateMyProfile
);

// @desc    Change user's password
// @route   PATCH /api/users/password
// @access  Private
router.patch(
    '/password',
    validateChangePassword,
    changePassword
);

// @desc    Upload profile picture
// @route   POST /api/users/profile/picture
// @access  Private
router.post(
    '/profile/picture',
    upload.single('profilePicture'),
    uploadProfilePicture
);

// @desc    Get user by ID
// @route   GET /api/users/:id
// @access  Private/Admin
router.get(
    '/:id',
    validateObjectId('id'),
    permissionMiddleware('users:view'),
    getUserById
);

// @desc    Update user (admin only)
// @route   PUT /api/users/:id
// @access  Private/Admin
router.put(
    '/:id',
    validateObjectId('id'),
    permissionMiddleware('users:edit'),
    validateAdminUpdateUser,
    updateUser
);

module.exports = router;
