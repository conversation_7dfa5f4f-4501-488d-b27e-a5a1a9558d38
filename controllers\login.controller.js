const SuperAdmin = require('../schema/superadmin.model');
const jwt = require('jsonwebtoken');

// Role hierarchy and permissions
const ROLE_PERMISSIONS = {
    superadmin: {
        permissions: ['*'],
        inherits: ['manager', 'employee']
    },
    manager: {
        permissions: [
            'applications:view',
            'applications:review',
            'applications:approve',
            'users:view'
        ],
        inherits: ['employee']
    },
    employee: {
        permissions: [
            'applications:create',
            'applications:view:own',
            'applications:edit:own',
            'profile:view',
            'profile:edit'
        ]
    },
    branchmanager: {
        permissions: [
            'applications:view',
            'applications:review',
            'reports:view',
            'users:view:branch'
        ],
        inherits: ['employee']
    },
    managingdirector: {
        permissions: [
            'applications:view',
            'applications:approve',
            'reports:view',
            'users:view:all'
        ],
        inherits: ['manager']
    }
};

// Get all permissions for a role including inherited ones
function getRolePermissions(role) {
    if (!ROLE_PERMISSIONS[role]) return [];
    
    const permissions = new Set(ROLE_PERMISSIONS[role].permissions);
    
    // Add permissions from inherited roles
    if (ROLE_PERMISSIONS[role].inherits) {
        ROLE_PERMISSIONS[role].inherits.forEach(inheritedRole => {
            getRolePermissions(inheritedRole).forEach(permission => {
                permissions.add(permission);
            });
        });
    }
    
    return Array.from(permissions);
}

// Login route
const login = async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ error: 'Email and password are required' });
        }

        // 1. Find the user by email
        const user = await SuperAdmin.findOne({ email, isActive: true });
        if (!user) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // 2. Compare passwords using the schema method
        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // 3. Get user permissions
        const permissions = getRolePermissions(user.role);
        
        // 4. Generate JWT token
        const token = jwt.sign(
            {
                email: user.email,
                role: user.role,
                userId: user._id.toString(),
                permissions,
                iat: Math.floor(Date.now() / 1000),
                exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
            },
            process.env.JWT_SECRET
        );

        // 5. Prepare user data for response (exclude sensitive info)
        const userData = {
            email: user.email,
            role: user.role,
            permissions
        }
        
        res.json({ 
            success: true,
            data: {
                token,
                user: userData
            }
        });

    } catch (err) {
        console.log(err);
        res.status(500).json({ error: 'Server error' });
    }
};

const createSuperAdmin = async (req, res) => {
    try {
        const superAdminData = {
            email: "<EMAIL>",
            password: "SecurePassword123",
            role: "superadmin",
            isActive: true,
        };

        // Check if a super admin already exists
        const existingAdmin = await SuperAdmin.findOne({ role: 'superadmin' });
        if (existingAdmin) {
            return res.status(400).json({ 
                success: false,
                message: 'A super admin already exists' 
            });
        }

        // Create new super admin using Mongoose model
        await SuperAdmin.create(superAdminData);

        res.status(201).json({ 
            success: true,
            message: 'SuperAdmin created successfully' 
        });
    } catch (err) {
        console.error('Error creating SuperAdmin:', err);
        res.status(500).json({ 
            success: false,
            message: 'Failed to create SuperAdmin',
            error: err.message 
        });
    }
};

const logout = (req, res) => {
    res.clearCookie('token', {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
    });
    res.json({ message: 'Logged out successfully' });  
}

const getCurrentUser = async (req, res) => {
    try {
        // The user information is already attached to req.user by the authMiddleware
        const user = req.user;
        
        // Return only the necessary user information (exclude sensitive data)
        res.json({
            success: true,
            data: {
                id: user.userId,
                email: user.email,
                role: user.role,
                // Add any other non-sensitive user info you want to expose
            }
        });
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ 
            success: false,
            message: 'Error fetching user profile',
            error: error.message 
        });
    }
};

module.exports = {
    login,
    logout,
    createSuperAdmin,
    getCurrentUser
}