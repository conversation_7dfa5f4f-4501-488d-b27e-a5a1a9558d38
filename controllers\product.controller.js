const Product = require("../schema/product.model");
const LoanType = require("../schema/loanType");
const { paginateModel, softDeleteById } = require("../Utils/index");

const createProduct = async (req, res) => {
  try {
    if (req.user.role === "superadmin") {
      console.log("request >>>>>>>>>>.", req.body);

      if (!req.body.productCode) {
        return res.status(400).json({ message: "Product code is required." });
      }

      const product = new Product(req.body);
      await product.save();

      res.json({ message: "Product created successfully", product });
    } else {
      res.status(403).json({ error: "Unauthorized" });
    }
  } catch (err) {
    if (err.code === 11000) {
      return res.status(400).json({ message: "Product code must be unique." });
    }

    console.error("Error creating product:", err);
    res.status(500).json({ error: "Server Error" });
  }
};

const deleteProduct = async (req, res) => {
  try {
    if (req.user.role === "superadmin") {
      const deletedProduct = await softDeleteById(Product, req.params.id, {
        product_status: "inactive",
      });

      if (!deletedProduct) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.json({
        message: "Product soft-deleted successfully",
        deletedProduct,
      });
    } else {
      res.status(403).json({ error: "Unauthorized" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: error.message });
  }
};

const getProduct = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await paginateModel(Product, {
      page,
      limit,
      filter: { isDeleted: false },
      populate: "loanTypeId",
    });

    res.status(200).json({ success: true, data: result });
  } catch (error) {
    console.error("Error getting products:", error.message);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

const getProductById = async (req, res) => {
  try {
    const product = await Product.findOne({
      _id: req.params.id,
      isDeleted: false,
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    res.status(200).json({
      success: true,
      data: product,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

module.exports = {
  createProduct,
  deleteProduct,
  getProduct,
  getProductById,
};
