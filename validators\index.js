const Joi = require('joi');
const { validate } = require('../middleware/validation.middleware');

// Common validation patterns
const patterns = {
    email: Joi.string().email().trim().lowercase().required(),
    password: Joi.string().min(8).required(),
    phone: Joi.string().pattern(/^[0-9]{10,15}$/),
    objectId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/, 'valid ObjectId'),
    name: Joi.string().trim().min(2).max(50),
    gender: Joi.string().valid('male', 'female', 'other', 'prefer_not_to_say'),
    date: Joi.date().iso(),
    role: Joi.string().valid('superadmin', 'employee', 'manager', 'branchmanager', 'managingdirector'),
};

// User validation schemas
const userSchemas = {
    // Register new user (admin only)
    register: Joi.object({
        email: patterns.email,
        password: patterns.password,
        role: patterns.role.required(),
        firstName: patterns.name.required(),
        middleName: patterns.name.allow(''),
        lastName: patterns.name.required(),
        phone: patterns.phone.required(),
        gender: patterns.gender.required(),
        dateOfBirth: patterns.date.required(),
        branch: patterns.objectId.when('role', {
            is: Joi.valid('superadmin', 'managingdirector'),
            then: Joi.optional(),
            otherwise: Joi.required()
        }),
        department: Joi.string().trim().required(),
        designation: Joi.string().trim().required(),
        dateOfJoining: patterns.date.required(),
        address: Joi.object({
            street: Joi.string().trim().required(),
            city: Joi.string().trim().required(),
            state: Joi.string().trim().required(),
            postalCode: Joi.string().trim().required(),
            country: Joi.string().trim().default('India')
        }).required()
    }),

    // Update profile
    updateProfile: Joi.object({
        firstName: patterns.name,
        middleName: patterns.name.allow(''),
        lastName: patterns.name,
        phone: patterns.phone,
        gender: patterns.gender,
        dateOfBirth: patterns.date,
        address: Joi.object({
            street: Joi.string().trim(),
            city: Joi.string().trim(),
            state: Joi.string().trim(),
            postalCode: Joi.string().trim(),
            country: Joi.string().trim()
        })
    }).min(1), // At least one field is required

    // Change password
    changePassword: Joi.object({
        currentPassword: patterns.password,
        newPassword: patterns.password.invalid(Joi.ref('currentPassword')).messages({
            'any.invalid': 'New password must be different from current password'
        }),
        confirmPassword: Joi.string().valid(Joi.ref('newPassword')).messages({
            'any.only': 'Passwords do not match'
        })
    }),

    // Admin update user
    adminUpdateUser: Joi.object({
        email: patterns.email,
        role: patterns.role,
        isActive: Joi.boolean(),
        branch: patterns.objectId,
        department: Joi.string().trim(),
        designation: Joi.string().trim(),
        dateOfJoining: patterns.date
    }).min(1)
};

// Application validation schemas
const applicationSchemas = {
    create: Joi.object({
        // Add your application creation validation schema here
    }),
    update: Joi.object({
        // Add your application update validation schema here
    }),
    submit: Joi.object({
        // Add your application submission validation schema here
    })
};

// Workflow validation schemas
const workflowSchemas = {
    reviewAction: Joi.object({
        action: Joi.string().valid('approve', 'reject', 'request_changes').required(),
        comments: Joi.string().trim().when('action', {
            is: 'approve',
            then: Joi.string().trim().allow(''),
            otherwise: Joi.string().trim().required()
        })
    })
};

// Export validation middleware functions
module.exports = {
    // User validations
    validateRegister: validate(userSchemas.register),
    validateUpdateProfile: validate(userSchemas.updateProfile),
    validateChangePassword: validate(userSchemas.changePassword),
    validateAdminUpdateUser: validate(userSchemas.adminUpdateUser),
    
    // Application validations
    validateCreateApplication: validate(applicationSchemas.create),
    validateUpdateApplication: validate(applicationSchemas.update),
    validateSubmitApplication: validate(applicationSchemas.submit),
    
    // Workflow validations
    validateReviewAction: validate(workflowSchemas.reviewAction, 'body'),
    
    // Common validators
    validateObjectId: (paramName) => (req, res, next) => {
        const schema = Joi.object({
            [paramName]: patterns.objectId.required()
        });
        return validate(schema, 'params')(req, res, next);
    }
};
