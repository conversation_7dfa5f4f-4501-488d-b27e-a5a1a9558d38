const express = require('express');
const router = express.Router();
const { authMiddleware, roleMiddleware } = require('../middleware/auth.middleware');
const pApplication = require('../controllers/PrimaryApplication.controller');
const ApplicationWorkflow = require('../controllers/applicationWorkflow.controller');
const { APPLICATION_STATUS } = require('../schema/primaryApplication.model');
const { <PERSON>rror<PERSON>and<PERSON> } = require('../Utils/common');

/**
 * @swagger
 * /primary-application/create:
 *   post:
 *     summary: Create a new primary application
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Application'
 *     responses:
 *       201:
 *         description: Application created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/create', 
    authMiddleware,
    roleMiddleware(['superadmin', 'employee']),
    pApplication.createPrimaryApplication
);

/**
 * @swagger
 * /primary-application/get:
 *   get:
 *     summary: Get a primary application by ID
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: primaryApplicationId
 *         schema:
 *           type: string
 *         required: true
 *         description: ID of the primary application
 *     responses:
 *       200:
 *         description: Primary application retrieved successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.get('/:primaryApplicationId', 
    authMiddleware,
    roleMiddleware(['superadmin', 'employee']),
    pApplication.getPrimaryApplication
);

/**
 * @swagger
 * /primary-application/loan-details:
 *   post:
 *     summary: Save loan details for an application
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoanDetails'
 *     responses:
 *       200:
 *         description: Loan details saved successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/loan-details', 
    authMiddleware,
    roleMiddleware(['superadmin', 'employee']),
    pApplication.saveLoanDetails
);

/**
 * @swagger
 * /primary-application/collateral-details:
 *   post:
 *     summary: Save collateral details for an application
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CollateralDetails'
 *     responses:
 *       200:
 *         description: Collateral details saved successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/collateral-details', 
    authMiddleware,
    roleMiddleware(['superadmin', 'employee']),
    pApplication.saveCollateralDetails
);

/**
 * @swagger
 * /applications/submit/{id}:
 *   post:
 *     summary: Submit an application for review
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Application ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               comments:
 *                 type: string
 *     responses:
 *       200:
 *         description: Application submitted successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Application not found
 */
router.post('/submit/:id', 
    authMiddleware, 
    roleMiddleware(['employee', 'manager', 'superadmin']),
    async (req, res, next) => {
        try {
            const application = await ApplicationWorkflow.submitApplication(
                req.params.id,
                req.user._id,
                req.user.role,
                req.body.comments || ''
            );
            res.json({ success: true, data: application });
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /applications/review/{id}:
 *   post:
 *     summary: Review an application (approve/reject/request changes)
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Application ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [approve, reject, request_changes, bypass]
 *                 description: Action to perform on the application
 *               comments:
 *                 type: string
 *                 description: Optional comments for the review
 *               nextReviewerId:
 *                 type: string
 *                 description: ID of the next reviewer (for multi-level approvals)
 *     responses:
 *       200:
 *         description: Review action completed successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Application not found
 */
router.post('/review/:id', 
    authMiddleware,
    roleMiddleware(['superadmin', 'manager', 'employee']),
    async (req, res, next) => {
        try {
            const application = await ApplicationWorkflow.reviewApplication({
                applicationId: req.params.id,
                userId: req.user._id,
                role: req.user.role,
                action: req.body.action,
                comments: req.body.comments || '',
                nextReviewerId: req.body.nextReviewerId
            });
            res.json({ success: true, data: application });
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /applications/workflow/{id}:
 *   get:
 *     summary: Get application workflow details
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Application ID
 *     responses:
 *       200:
 *         description: Application workflow details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApplicationWorkflow'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Application not found
 */
router.get('/workflow/:id', 
    authMiddleware,
    async (req, res, next) => {
        try {
            const workflow = await ApplicationWorkflow.getApplicationWorkflow(
                req.params.id,
                req.user._id,
                req.user.role
            );
            res.json({ success: true, data: workflow });
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /applications/history/{id}:
 *   get:
 *     summary: Get application approval history
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Application ID
 *     responses:
 *       200:
 *         description: Application approval history
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ApprovalHistory'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Application not found
 */
router.get('/history/:id', 
    authMiddleware,
    async (req, res, next) => {
        try {
            const history = await ApplicationWorkflow.getApplicationHistory(
                req.params.id,
                req.user._id,
                req.user.role
            );
            res.json({ success: true, data: history });
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /applications/for-review:
 *   get:
 *     summary: Get applications available for review by the current user
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [0, 1, 2, 3, 4, 5]
 *         description: Filter by application status
 *     responses:
 *       200:
 *         description: List of applications available for review
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Application'
 *       401:
 *         description: Unauthorized
 */
router.get('/for-review', 
    authMiddleware,
    roleMiddleware(['superadmin', 'manager', 'employee']),
    async (req, res, next) => {
        try {
            const applications = await ApplicationWorkflow.getApplicationsForReview(
                req.user._id,
                req.user.role,
                req.query.status
            );
            res.json({ success: true, data: applications });
        } catch (error) {
            next(error);
        }
    }
);

module.exports = router;
