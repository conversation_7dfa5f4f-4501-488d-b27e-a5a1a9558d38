const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } = require('../utils/errorHandler');

/**
 * Middleware to validate request data against a Joi schema
 * @param {Joi.Schema} schema - Joi validation schema
 * @param {string} [source='body'] - Request property to validate (body, params, query)
 * @returns {Function} Express middleware function
 */
const validate = (schema, source = 'body') => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req[source], {
            abortEarly: false, // Return all validation errors
            allowUnknown: true, // Allow unknown keys that will be ignored
            stripUnknown: true, // Remove unknown elements
        });

        if (error) {
            const errors = error.details.map((detail) => ({
                field: detail.path.join('.'),
                message: detail.message.replace(/["']/g, ''),
                type: detail.type,
            }));

            return next(new ErrorHandler('Validation failed', 400, errors));
        }

        // Replace the request data with the validated and sanitized data
        req[source] = value;
        next();
    };
};

module.exports = validate;
